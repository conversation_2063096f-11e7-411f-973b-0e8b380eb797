defmodule Drops.Relation.Plugins.Queryable.Operations.Order.FieldsCompiler do
  alias Drops.Relation.Schema

  import Ecto.Query

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    order_value = Keyword.get(opts, :order)

    if order_value do
      case validate_order_fields(order_value, relation.schema) do
        [] ->
          # No validation errors
          valid_order_fields =
            visit(order_value, %{schema: relation.schema, filter: :valid_fields})

          result_query = apply_order_by(query, valid_order_fields)
          {:ok, result_query}

        errors ->
          {:error, errors}
      end
    else
      {:ok, query}
    end
  end

  def visit(order_value, %{schema: schema, filter: :valid_fields}) do
    field_names = visit(schema, %{extract: :field_names})

    case order_value do
      field when is_atom(field) ->
        if field in field_names, do: field, else: nil

      fields when is_list(fields) ->
        Enum.filter(fields, fn
          field when is_atom(field) ->
            field in field_names

          {direction, field} when direction in [:asc, :desc] and is_atom(field) ->
            field in field_names

          _ ->
            false
        end)

      _ ->
        nil
    end
  end

  def visit(%Schema{fields: fields}, %{extract: :field_names}) do
    Enum.map(fields, &visit(&1, %{extract: :name}))
  end

  def visit(%Schema.Field{name: name}, %{extract: :name}), do: name

  # Validation functions
  defp validate_order_fields(order_value, schema) do
    field_names = visit(schema, %{extract: :field_names})

    case order_value do
      field when is_atom(field) ->
        if field in field_names do
          []
        else
          ["field :#{field} is not defined in the schema"]
        end

      fields when is_list(fields) ->
        Enum.flat_map(fields, fn
          field when is_atom(field) ->
            if field in field_names do
              []
            else
              ["field :#{field} is not defined in the schema"]
            end

          {direction, field} when direction in [:asc, :desc] and is_atom(field) ->
            if field in field_names do
              []
            else
              ["field :#{field} is not defined in the schema"]
            end

          invalid ->
            ["invalid order specification: #{inspect(invalid)}"]
        end)

      invalid ->
        ["invalid order value: #{inspect(invalid)}"]
    end
  end

  defp apply_order_by(queryable, nil), do: queryable
  defp apply_order_by(queryable, []), do: queryable

  defp apply_order_by(queryable, order_by) when is_atom(order_by) do
    order_by(queryable, ^order_by)
  end

  defp apply_order_by(queryable, order_by) when is_list(order_by) do
    Enum.reduce(order_by, queryable, fn order_spec, query ->
      case order_spec do
        field when is_atom(field) ->
          order_by(query, ^field)

        {direction, field} when direction in [:asc, :desc] and is_atom(field) ->
          order_by(query, [{^direction, ^field}])

        _ ->
          query
      end
    end)
  end
end
