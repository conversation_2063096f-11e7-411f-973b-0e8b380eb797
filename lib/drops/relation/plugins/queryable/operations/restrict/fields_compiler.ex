defmodule Drops.Relation.Plugins.Queryable.Operations.Restrict.FieldsCompiler do
  alias Drops.Relation.Schema

  import Ecto.Query

  @spec visit(map(), map()) :: Ecto.Query.t()
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    valid_field_opts = visit(opts, %{schema: relation.schema, filter: :valid_fields})

    Enum.reduce(valid_field_opts, query, fn {field_name, value}, acc_query ->
      field_struct = Schema.find_field(relation.schema, field_name)
      visit({field_struct, value}, %{query: acc_query})
    end)
  end

  def visit(opts, %{schema: schema, filter: :valid_fields}) when is_list(opts) do
    field_names = visit(schema, %{extract: :field_names})

    Enum.filter(opts, fn {field, _value} ->
      field in field_names
    end)
  end

  def visit(%Schema{fields: fields}, %{extract: :field_names}) do
    Enum.map(fields, &visit(&1, %{extract: :name}))
  end

  def visit(%Schema.Field{name: name}, %{extract: :name}), do: name

  def visit({%Schema.Field{name: field_name}, value}, %{query: query}) when is_list(value) do
    where(query, [r], field(r, ^field_name) in ^value)
  end

  def visit({%Schema.Field{name: field_name, meta: meta}, nil}, %{query: query}) do
    if Map.get(meta, :nullable, true) do
      where(query, [r], is_nil(field(r, ^field_name)))
    else
      query
    end
  end

  def visit({%Schema.Field{name: field_name}, value}, %{query: query}) when is_boolean(value) do
    where(query, [r], field(r, ^field_name) == ^value)
  end

  def visit({%Schema.Field{name: field_name}, value}, %{query: query}) do
    where(query, [r], field(r, ^field_name) == ^value)
  end
end
