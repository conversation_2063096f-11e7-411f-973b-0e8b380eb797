defmodule Drops.Relation.Plugins.Queryable.Operations.Restrict.FieldsCompiler do
  alias Drops.Relation.Schema

  import Ecto.Query

  @spec visit(map(), map()) :: {:ok, Ecto.Query.t()} | {:error, [String.t()]}
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    valid_field_opts = visit(opts, %{schema: relation.schema, filter: :valid_fields})

    # Collect all validation errors first
    errors =
      Enum.flat_map(valid_field_opts, fn {field_name, value} ->
        field_struct = Schema.find_field(relation.schema, field_name)
        validate_field_value(field_struct, value)
      end)

    case errors do
      [] ->
        # No errors, proceed with building the query
        result_query =
          Enum.reduce(valid_field_opts, query, fn {field_name, value}, acc_query ->
            field_struct = Schema.find_field(relation.schema, field_name)
            apply_field_condition({field_struct, value}, %{query: acc_query})
          end)

        {:ok, result_query}

      errors ->
        {:error, errors}
    end
  end

  def visit(opts, %{schema: schema, filter: :valid_fields}) when is_list(opts) do
    field_names = visit(schema, %{extract: :field_names})

    Enum.filter(opts, fn {field, _value} ->
      field in field_names
    end)
  end

  def visit(%Schema{fields: fields}, %{extract: :field_names}) do
    Enum.map(fields, &visit(&1, %{extract: :name}))
  end

  def visit(%Schema.Field{name: name}, %{extract: :name}), do: name

  # Validation functions
  defp validate_field_value(%Schema.Field{name: field_name, meta: meta}, nil) do
    if Map.get(meta, :nullable, true) do
      []
    else
      ["#{field_name} is not nullable, comparing to `nil` is not allowed"]
    end
  end

  defp validate_field_value(%Schema.Field{name: field_name} = field, value)
       when is_boolean(value) do
    # Check if we're comparing a boolean value to a non-boolean field
    case is_boolean_field?(field) do
      true ->
        []

      false ->
        [
          "#{field_name} is not a boolean field, comparing to boolean value `#{value}` is not allowed"
        ]
    end
  end

  defp validate_field_value(%Schema.Field{}, value) when is_list(value) do
    # List values are always valid for IN expressions
    []
  end

  defp validate_field_value(%Schema.Field{}, _value) do
    # Other values are valid by default
    []
  end

  # Helper function to determine if a field is semantically boolean based on metadata
  defp is_boolean_field?(%Schema.Field{type: :boolean}), do: true

  defp is_boolean_field?(%Schema.Field{type: :integer, meta: meta}) do
    # Check if this is a boolean field stored as integer (common in SQLite)
    # Look for boolean-like default values
    case Map.get(meta, :default) do
      default when default in [0, 1, true, false] -> true
      _ -> false
    end
  end

  defp is_boolean_field?(_), do: false

  # Query building functions (renamed from visit to apply_field_condition)
  defp apply_field_condition({%Schema.Field{name: field_name}, value}, %{query: query})
       when is_list(value) do
    where(query, [r], field(r, ^field_name) in ^value)
  end

  defp apply_field_condition({%Schema.Field{name: field_name, meta: meta}, nil}, %{query: query}) do
    # We already validated that nullable fields can be nil, so this is safe
    if Map.get(meta, :nullable, true) do
      where(query, [r], is_nil(field(r, ^field_name)))
    else
      # This should never happen due to validation, but keep as fallback
      query
    end
  end

  defp apply_field_condition({%Schema.Field{name: field_name}, value}, %{query: query})
       when is_boolean(value) do
    where(query, [r], field(r, ^field_name) == ^value)
  end

  defp apply_field_condition({%Schema.Field{name: field_name}, value}, %{query: query}) do
    where(query, [r], field(r, ^field_name) == ^value)
  end
end
