defmodule Drops.Relation.Plugins.Queryable.Operations.Preload.AssociationCompiler do
  import Ecto.Query

  @spec visit(map(), map()) :: Ecto.Query.t()
  def visit(relation, %{query: query, opts: opts}) when is_list(opts) do
    preload_value = Keyword.get(opts, :preload)

    if preload_value do
      valid_preloads = visit(preload_value, %{relation: relation, filter: :valid_associations})
      apply_preloads(query, valid_preloads)
    else
      query
    end
  end

  def visit(preload_value, %{relation: relation, filter: :valid_associations}) do
    available_associations = visit(relation, %{extract: :association_names})

    case preload_value do
      association when is_atom(association) ->
        if association in available_associations, do: association, else: nil

      associations when is_list(associations) ->
        filter_valid_associations(associations, available_associations)

      _ ->
        nil
    end
  end

  def visit(relation, %{extract: :association_names}) do
    relation_module = relation.__struct__

    if function_exported?(relation_module, :__schema_module__, 0) do
      schema_module = relation_module.__schema_module__()

      if function_exported?(schema_module, :__schema__, 1) do
        schema_module.__schema__(:associations)
      else
        []
      end
    else
      []
    end
  end

  # Private functions

  defp filter_valid_associations(associations, available_associations) do
    Enum.filter(associations, fn
      association when is_atom(association) ->
        association in available_associations

      {association, _nested} when is_atom(association) ->
        # For nested associations like [user: :profile], validate the root association
        # Note: We don't validate nested associations here as they depend on the target schema
        association in available_associations

      _ ->
        false
    end)
  end

  defp apply_preloads(queryable, nil), do: queryable
  defp apply_preloads(queryable, []), do: queryable

  defp apply_preloads(queryable, preloads) when is_atom(preloads) do
    from(q in queryable, preload: ^preloads)
  end

  defp apply_preloads(queryable, preloads) when is_list(preloads) do
    from(q in queryable, preload: ^preloads)
  end
end
